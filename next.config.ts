import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // 环境变量配置
  env: {
    // 自定义环境变量可以在这里定义
    // 这些变量会在构建时被内联到代码中
  },

  // 图片配置
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'jwsmedstatic.oss-cn-hangzhou.aliyuncs.com',
        port: '',
        pathname: '/cms/**',
      },
      // 可以添加其他图片域名
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '1337',
        pathname: '/uploads/**',
      },
    ],
    // 图片格式配置
    formats: ['image/webp', 'image/avif'],
    // 图片质量配置
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },

  // 实验性功能
  experimental: {
    // 暂时禁用类型化路由以避免类型错误
    typedRoutes: false,
  },

  // 国际化配置 - 在 App Router 中，这主要用于 SEO 和静态生成
  trailingSlash: false,

  // 重定向配置
  async redirects() {
    return [
      // 可以根据环境配置不同的重定向规则
    ]
  },

  // 重写配置
  async rewrites() {
    return [
      // 可以根据环境配置不同的重写规则
    ]
  },

  // 头部配置
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          // 安全头部配置
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
        ],
      },
    ]
  },
};

export default nextConfig;
