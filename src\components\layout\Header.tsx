'use client'

import { motion } from 'motion/react'
import Link from 'next/link'
import { useState } from 'react'
import Image from 'next/image'
import LanguageSwitcher from '@/components/ui/LanguageSwitcher'
import { useTranslation } from '@/lib/i18n/client'
import type { IHeader, ISite } from '@/types/strapi'

import Logo from '/public/images/logo.png'

interface HeaderProps {
  data?: IHeader | null
  siteData?: ISite | null
}

export default function Header({ data, siteData }: HeaderProps) {
  const { dictionary, locale } = useTranslation()
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [menus, setMenus] = useState(data?.menus || [])
  const [site, setSite] = useState(siteData || null)
  const [activeMenu, setActiveMenu] = useState<number | null>(null)

  return (
    <motion.header
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.6 }}
      className="fixed top-0 right-0 left-0 z-50 bg-black"
    >
      {/* Desktop Layout */}
      <div className="hidden lg:block">
        <div className="mx-auto max-w-full px-4 sm:px-6 lg:px-42 ">
          {/* Logo Section */}
          <div className="w-[1200px] flex items-center justify-center py-4 relative m-auto">
            <motion.div whileHover={{ scale: 1 }} className="flex-shrink-0">
              <Link href={`/${locale}`} className="block">
                <Image
                  src={site?.logo?.url || Logo}
                  alt="Lumii Logo"
                  width={120}
                  height={40}
                  className="h-8 w-auto"
                />
              </Link>
            </motion.div>
            {/* Language Switcher */}
            <div className="absolute top-4 right-[2px]">
              <LanguageSwitcher />
            </div>
          </div>

          {/* Navigation Section */}
          <div className="flex items-center justify-between">
            <nav className="flex flex-1 items-center justify-center pb-2">
              <div className="flex space-x-8">
                {menus.map((item, index) => {
                  const isActive = activeMenu === item.id
                  return (
                    <motion.div
                      key={item.id}
                      initial={{ opacity: 0, y: -20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: index * 0.1 }}
                    >
                      <Link
                        href={`/${locale}`}
                        onClick={() => setActiveMenu(item.id)}
                        className={`relative px-3 py-2 text-[14px] whitespace-nowrap text-white transition-all duration-200 hover:font-[600] ${
                          isActive
                            ? 'font-semibold after:absolute after:bottom-0 after:left-1/2 after:h-0.5 after:w-8 after:-translate-x-1/2 after:transform after:bg-white after:content-[""]'
                            : 'font-light hover:font-semibold hover:after:absolute hover:after:bottom-0 hover:after:left-1/2 hover:after:h-0.5 hover:after:w-8 hover:after:-translate-x-1/2 hover:after:transform hover:after:bg-white hover:after:content-[""]'
                        }`}
                      >
                        {item.label}
                      </Link>
                    </motion.div>
                  )
                })}
              </div>
            </nav>
          </div>
        </div>
      </div>

      {/* Mobile Layout */}
      <div className="lg:hidden">
        <div className="mx-auto max-w-full px-4 sm:px-6">
          <div className="flex h-16 items-center justify-between">
            {/* Logo */}
            <motion.div whileHover={{ scale: 1.05 }} className="flex-shrink-0">
              <Link href={`/${locale}`} className="block">
                <Image src={Logo} alt="Lumii Logo" width={120} height={40} className="h-8 w-auto" />
              </Link>
            </motion.div>

            {/* Mobile menu button */}
            <div>
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="p-2 text-white hover:text-gray-300 focus:text-gray-300 focus:outline-none"
                aria-label="Toggle menu"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  {isMenuOpen ? (
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  ) : (
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 6h16M4 12h16M4 18h16"
                    />
                  )}
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="space-y-1 border-t border-gray-800 bg-black px-2 pt-2 pb-4">
              {menus.map((item) => (
                <Link
                  key={item.id}
                  href={`/${locale}`}
                  className="block border-b border-gray-800 px-3 py-3 text-base font-medium text-white last:border-b-0 hover:text-gray-300"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.label}
                </Link>
              ))}
              <div className="mt-2  px-3 py-3">
                <LanguageSwitcher />
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </motion.header>
  )
}
