import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

/**
 * 合并 Tailwind CSS 类名的工具函数
 * 使用 clsx 处理条件类名，使用 tailwind-merge 解决类名冲突
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * 格式化数字为千分位格式
 */
export function formatNumber(num: number): string {
  return new Intl.NumberFormat('zh-CN').format(num)
}

/**
 * 延迟函数，用于动画或异步操作
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 生成随机 ID
 */
export function generateId(): string {
  return Math.random().toString(36).substr(2, 9)
}

// 图片相关工具函数已移动到 @/lib/utils/image
// 为了保持向后兼容，重新导出一些常用函数
export {
  isValidStrapiImage as hasValidImage,
  getBestImageUrl as getStrapiImageUrl,
  generateImageAlt,
  getPresetSizes
} from '@/lib/utils/image'
