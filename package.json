{"name": "lumii-offical", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "dotenv -e .env.prod -- next build", "start": "dotenv -e .env.prod -- next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "clean": "rm -rf .next out dist", "analyze": "ANALYZE=true npm run build", "build:dev": "dotenv -e .env.dev -- next build", "start:dev": "dotenv -e .env.dev -- next start", "build:test": "dotenv -e .env.test -- next build", "start:test": "dotenv -e .env.test -- next start", "build:pre": "dotenv -e .env.pre -- next build", "start:pre": "dotenv -e .env.pre -- next start"}, "dependencies": {"clsx": "^2.1.1", "lucide-react": "^0.525.0", "motion": "^12.23.7", "next": "15.4.5", "react": "19.1.1", "react-dom": "19.1.1", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20.19.9", "@types/react": "19.1.9", "@types/react-dom": "19.1.7", "dotenv-cli": "^9.0.0", "eslint": "^9", "eslint-config-next": "15.4.5", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4", "typescript": "^5"}, "overrides": {"@types/react": "19.1.9", "@types/react-dom": "19.1.7"}}