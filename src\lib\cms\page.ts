/**
 * Page API函数
 */

import type { IPage } from '@/types/strapi'
import { fetchStrapiCollection } from './client'
import { API_ENDPOINTS, PAGE_POPULATE } from './config'

/**
 * 获取page页面数据
 */
export const getPage = async (locale: string, name: string): Promise<IPage | null> => {
  try {
    const response = await fetchStrapiCollection<IPage>(
      API_ENDPOINTS.PAGE,
      locale,
      { populate: PAGE_POPULATE }
      
    )
    // 返回页面数据
    return response?.data?.filter(item => item.name === name)[0] || null
  } catch (error) {
    console.error('Failed to fetch About Us page:', error)
    return null
  }
}
