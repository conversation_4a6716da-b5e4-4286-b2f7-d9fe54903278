import { getPage } from '@/lib/cms/page'
import Image from 'next/image'
import Icon from '@/components/ui/Icon';

// 区块组件
const BlockRenderer = ({ block }: { block: any }) => {
  const { __component, title, description, media} = block

  // 根据不同的组件类型渲染不同的布局
  switch (__component) {
    case 'shared.block':
      // 第一个模块：品牌介绍模块 - 上中下结构
      if (
        title === 'Lumii：源自女神的微光之美' ||
        title === 'Lumii: The Beauty of Light Originating from the Goddess'
      ) {
        return (
          <section className="py-10 lg:py-20">
            <div className="px-6 lg:px-0">
              <div className="flex flex-col">
                {/* 上部：图片 */}
                <div className="mb-5 lg:mb-12">
                  {media && (
                    <div className="relative">
                      <Image
                        src={media.url}
                        alt={title || ''}
                        width={1200}
                        height={0}
                        className="h-auto w-full object-cover"
                      />
                    </div>
                  )}
                </div>

                {/* 中部：标题 */}
                <div className="text-center">
                  <h2 className="mb-2 text-[20px] font-[500] lg:text-[22px]">{title}</h2>
                </div>

                {/* 下部：描述文字 */}

                <p className="text-justify text-[14px] leading-[24px] text-[#444444]">
                  {description}
                </p>

              </div>
            </div>
          </section>
        )
      }
      if (title == 'Fernando Autran') {
        return (
          <section className="py-10 lg:py-20">
            <div className="container mx-auto px-4">
              <div className="flex flex-col lg:grid lg:grid-cols-2 lg:items-start lg:space-y-0">
                {/* 移动端：标题 */}
                <div className="lg:hidden">
                  <h2 className="mb-3 text-[20px]">{title}</h2>
                </div>

                {/* 左侧图片 */}
                <div className="order-2 lg:order-1">
                  {media && (
                    <Image
                      src={media.url}
                      alt={title || ''}
                      width={500}
                      height={320}
                      className="h-auto w-full object-cover lg:h-[320px] lg:w-[500px]"
                    />
                  )}
                </div>

                {/* 右侧内容 */}
                <div className="order-1 flex h-full flex-col justify-between lg:order-2">
                  <div>
                    {/* PC端：标题 */}
                    <h2 className="mb-8 hidden text-4xl font-[30px] lg:block">{title}</h2>

                    {/* 描述文字 */}
                    <div className="text-sm leading-[28px] text-[#444444] mb-7.5 lg:mb-0">
                      {description}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        )
      }

    default:
      return null
  }
}

export default async function Home({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params
  const name = locale === 'en-US' ? 'Brand Introduction' : '品牌介绍'
  const pageData = await getPage(locale, name)

  return (
    <div className="pt-16 lg:pt-24">

      {/* Hero Banner */}
      <div className="relative">
        {pageData?.heroes.map((item: any) => (
          <div key={item.id}>
            <Image
              key={item.id}
              src="https://jwsmedstatic.oss-cn-hangzhou.aliyuncs.com/cms/2159c9cb-2ea6-4855-af1f-e95ad6500326-%E4%BD%8D%E5%9B%BE%E5%A4%87%E4%BB%BD%208%402x.png"
              alt={item.title || ''}
              width={1200}
              height={800}
              sizes="100vw"
              className="w-full h-auto object-cover"
            />
            <div className="absolute inset-0 top-10 left-8 text-white lg:top-35 lg:left-42.5 lg:w-[360px]">
              <h1 className="text-[20px] font-[500] lg:mb-8 lg:text-[36px]">{item.title}</h1>
              <span className="hidden text-sm lg:block"> {item.description}</span>
            </div>
          </div>
        ))}
      </div>

      {/* Blocks Content */}
      <div className="bg-[#FAF6F2]">
        <div className="md:mx-auto md:w-[1200px] md:max-w-none">
          {pageData?.blocks.map((block: any, index: number) => (
            <div key={block.id || index}>
              <BlockRenderer block={block} />
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
