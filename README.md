# Lumii Official Website

> 🚀 现代化企业官方网站，创新驱动数字化未来

## 📋 项目简介

Lumii Official Website 是一个现代化的企业官方网站，采用最新前端技术栈构建，为企业提供专业、时尚、高性能的数字化展示平台。项目注重用户体验、性能优化和可维护性，是企业数字化转型的理想选择。

## ✨ 核心特性

- 🎨 **现代化设计** - 简洁优雅的界面，支持响应式布局
- ⚡ **高性能** - 基于 Next.js 的 SSR/SSG，极速加载体验
- 🎭 **流畅动画** - Motion 动画库打造丝滑交互效果
- 📱 **移动优先** - 完美适配各种设备和屏幕尺寸
- 🔧 **开发友好** - TypeScript + 现代工具链，提升开发效率

## 🛠️ 技术栈

- **框架**: Next.js 15.4.3 + React 19.1.0
- **语言**: TypeScript 5.x
- **样式**: Tailwind CSS 4.x
- **动画**: Motion (Framer Motion)
- **工具**: ESLint + Prettier

> 📖 详细的技术架构说明请参考 [ARCHITECTURE.md](docs/ARCHITECTURE.md)

## 📁 项目结构

```
lumii-official/
├── src/
│   ├── app/              # Next.js App Router
│   ├── components/       # React 组件
│   ├── lib/             # 工具函数
│   └── types/           # 类型定义
├── docs/                # 项目文档
└── public/              # 静态资源
```

## 🚀 快速开始

### 环境要求
- Node.js 18.0+
- npm 9.0+

### 安装和运行
```bash
# 克隆项目
git clone <repository-url>
cd lumii-official

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看网站效果。

### 常用命令
```bash
npm run build     # 构建生产版本
npm run start     # 启动生产服务器
npm run lint      # 代码检查
npm run format    # 代码格式化
```

> 🔧 完整的开发指南请参考 [DEVELOPMENT.md](docs/DEVELOPMENT.md)

## 📖 文档导航

- **[技术架构](docs/ARCHITECTURE.md)** - 详细的系统架构和技术选型说明
- **[开发指南](docs/DEVELOPMENT.md)** - 完整的开发环境配置和工作流程
- **[环境配置](docs/ENVIRONMENT.md)** - 多环境配置管理系统使用指南
- **[API 文档](#)** - 接口文档（待完善）
- **[部署指南](#)** - 生产环境部署说明（待完善）

## 🌟 项目亮点

### 技术创新
- 采用 Next.js 15 最新 App Router 架构
- 集成 React 19 最新特性
- 使用 Tailwind CSS 4.x 最新版本

### 用户体验
- 响应式设计，完美适配所有设备
- 流畅的页面切换和交互动画
- 优化的加载性能和 SEO 支持

### 开发体验
- 完整的 TypeScript 类型安全
- 现代化的开发工具链
- 组件化架构，易于维护和扩展

## 🤝 参与贡献

我们欢迎社区贡献！请遵循以下步骤：

1. **Fork** 项目到您的 GitHub 账户
2. **创建分支** `git checkout -b feature/amazing-feature`
3. **提交更改** `git commit -m 'Add amazing feature'`
4. **推送分支** `git push origin feature/amazing-feature`
5. **创建 Pull Request**

### 贡献指南
- 遵循现有的代码风格和规范
- 添加适当的测试用例
- 更新相关文档
- 确保所有检查通过

## 📄 许可证

本项目采用 MIT 许可证 - 详情请查看 [LICENSE](LICENSE) 文件。

## 📞 联系方式

- **项目仓库**: [GitHub](https://github.com/lumii/lumii-official)
- **问题反馈**: [Issues](https://github.com/lumii/lumii-official/issues)
- **官方网站**: [https://lumii.com](https://lumii.com)
- **邮箱**: <EMAIL>

---

<div align="center">

**Lumii** - 创新驱动数字化未来 🚀

Made with ❤️ by Lumii Team

</div>
