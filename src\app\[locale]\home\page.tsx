import { getPage } from '@/lib/cms/page'
import Image from 'next/image'

// 区块组件
const BlockRenderer = ({ block }: { block: any }) => {
  const { __component, title, description, media, buttons } = block

  // 根据不同的组件类型渲染不同的布局
  switch (__component) {
    case 'shared.block':
      // 根据标题判断是品牌介绍还是产品介绍
      if (title === 'Brand Introduction' || title === '品牌介绍') {
        return (
          <section className="py-10 lg:py-20">
            <div className="container mx-auto px-4 lg:px-0">
              <div className="flex flex-col lg:flex-row lg:items-start lg:gap-20">
                {/* 移动端：标题 */}
                <div className="lg:hidden">
                  <h2 className="mb-3 text-[20px]">{title}</h2>
                </div>

                {/* 左侧图片 */}
                <div className="order-2 lg:order-1 lg:flex-1">
                  {media && (
                    <Image
                      src={media.url}
                      alt={title || ''}
                      width={500}
                      height={320}
                      className="w-full h-auto lg:w-full lg:h-auto object-cover"
                    />
                  )}
                </div>

                {/* 右侧内容 */}
                <div className="order-1 flex h-full flex-col lg:order-2 lg:flex-1 lg:justify-between">
                  <div>
                    {/* PC端：标题 */}
                    <h2 className="hidden lg:block mb-8 text-4xl font-[30px]">{title}</h2>

                    {/* 描述文字 */}
                    <div className="text-[#444444] text-sm leading-[28px] line-clamp-responsive text-justify">
                        {description}
                    </div>
                  </div>

                  {/* 按钮区域 */}
                  {buttons && buttons.length > 0 && (
                    <div className="flex gap-4 mt-4 mb-7 lg:mt-8 lg:mb-0">
                      {buttons.map((button: any, idx: number) => (
                        <button
                          key={idx}
                          className="bg-black py-1.5 px-11 lg:px-12 lg:py-3 text-sm lg:text-base text-white transition-colors hover:bg-gray-800"
                        >
                          {button.label}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </section>
        )
      }

      // 产品介绍
      if (title === 'Products' || title === '产品') {
        return (
          <section className="py-10 lg:py-20">
            <div className="container mx-auto px-4 lg:px-0">
              <div className="flex flex-col lg:flex-row lg:items-start lg:gap-20">
                {/* 移动端：标题 */}
                <div className="lg:hidden">
                  <h2 className="mb-3 text-[20px]">{title}</h2>
                </div>

                {/* 左侧内容 */}
                <div className="flex h-full flex-col lg:flex-1 lg:justify-between">
                  <div>
                    {/* PC端：标题 */}
                    <h2 className="hidden lg:block mb-8 text-4xl font-[30px]">{title}</h2>

                    {/* 描述文字 */}
                    <div className="text-[#444444] text-sm leading-[28px] line-clamp-responsive text-justify">
                        {description}
                    </div>
                  </div>

                  {/* 按钮区域 */}
                  {buttons && buttons.length > 0 && (
                    <div className="flex gap-4 mt-4 mb-7 lg:mt-8 lg:mb-0 ">
                      {buttons.map((button: any, idx: number) => (
                        <button
                          key={idx}
                          className="bg-black py-1.5 px-11 lg:px-12 lg:py-3 text-sm lg:text-base text-white transition-colors hover:bg-gray-800"
                        >
                          {button.label}
                        </button>
                      ))}
                    </div>
                  )}
                </div>

                {/* 右侧图片 */}
                <div className="flex justify-center lg:justify-end lg:flex-1">
                  {media && (
                    <Image
                      src={media.url}
                      alt={title || ''}
                      width={500}
                      height={320}
                      className="w-full h-auto lg:w-full lg:h-auto object-cover"
                    />
                  )}
                </div>
              </div>
            </div>
          </section>
        )
      }

      // 授权诊所
      if (title === 'Authorised clinics' || title === '授权诊所') {
        return (
          <section className="py-10 lg:py-20">
            <div className="container mx-auto px-4 lg:px-0">
              <div className="flex h-full flex-col justify-between">
                <div>
                  <h2 className="mb-10 lg:mb-15 text-[20px] lg:text-4xl font-[30px] text-center">{title}</h2>

                  {/* 诊所大图 */}
                  <div className="mb-8">
                    {media && (
                      <Image
                        src={media.url}
                        alt={title || ''}
                        width={1200}
                        height={600}
                        className="w-full h-auto lg:h-96 lg:object-cover object-contain"
                      />
                    )}
                  </div>

                  {/* 描述文字 */}
                  {description && (
                    <div className="mb-10">
                      <div className="text-[#444444] text-sm leading-[28px] lg:line-clamp-none line-clamp-3">
                        {description}
                      </div>
                    </div>
                  )}
                </div>

                {/* 按钮区域 */}
                {buttons && buttons.length > 0 && (
                  <div className="text-center">
                    <div className="flex justify-center gap-4">
                      {buttons.map((button: any, idx: number) => (
                        <button
                          key={idx}
                          className="bg-black px-11 lg:px-12 py-1.5 lg:py-3 text-sm lg:text-base text-white transition-colors hover:bg-gray-800"
                        >
                          {button.label}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </section>
        )
      }

      return null

    case 'blocks.brand-introduction':
      return (
        <section className="py-10 lg:py-20">
          <div className="container mx-auto px-4 lg:px-0">
            <div className="flex flex-col lg:flex-row lg:items-start lg:gap-20">
              {/* 左侧图片 */}
              <div className="order-2 lg:order-1 lg:flex-1">
                {media && (
                  <Image
                    src={media.url}
                    alt={title || ''}
                    width={500}
                    height={320}
                    className="w-full h-auto lg:w-full lg:h-auto object-cover"
                  />
                )}
              </div>

              {/* 右侧内容 */}
              <div className="order-1 flex h-full flex-col lg:order-2 lg:flex-1 lg:justify-between">
                <div>
                  <h2 className="mb-3 lg:mb-8 text-[20px] lg:text-4xl font-[30px]">{title}</h2>
                  <div className="text-[#444444] text-sm leading-[28px] line-clamp-2 lg:line-clamp-6 text-justify">
                    {description}
                  </div>
                </div>

                {/* 按钮区域 */}
                {buttons && buttons.length > 0 && (
                  <div className="flex gap-4 mt-4 lg:mt-8 lg:justify-end lg:items-end">
                    {buttons.map((button: any, idx: number) => (
                      <button
                        key={idx}
                        className="bg-black px-11 lg:px-12 py-1.5 lg:py-3 text-sm lg:text-base text-white transition-colors hover:bg-gray-800"
                      >
                        {button.label}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </section>
      )

    case 'shared.deep-block':
      // 处理深度嵌套的blocks，如用户案例和医生信息
      if (title === 'User Cases' || title === '客户案例') {
        return (
          <section className="py-10 lg:py-20">
            <div className="container mx-auto px-4 lg:px-0">
              <div className="flex h-full flex-col justify-between">
                <div>
                  <h2 className="mb-10 lg:mb-15 text-center text-[20px] lg:text-4xl font-[30px]">{title}</h2>

                  {/* 用户案例网格 */}
                  <div className="mb-10 flex flex-col space-y-6 lg:flex-row lg:justify-between lg:space-y-0 lg:space-x-2">
                    {block.blocks?.map((caseBlock: any, idx: number) => (
                      <div key={idx} className="group relative">
                        <div className="relative overflow-hidden ">
                          {caseBlock.media?.url ? (
                            <>
                              <Image
                                src={caseBlock.media.url}
                                alt={caseBlock.title || `Case ${idx + 1}`}
                                width={340}
                                height={420}
                                className="w-full h-auto lg:max-w-[340px] lg:max-h-[420px] object-cover transition-transform duration-300 lg:group-hover:scale-105"
                              />

                              {/* 移动端：直接显示的模态框 */}
                              <div className="lg:hidden absolute bottom-0 left-0 right-0 bg-black/80 text-white px-6 pt-5 pb-7">
                                <h3 className="text-[20px] mb-4 font-bold">
                                  {caseBlock.title}
                                </h3>
                                <p className="text-sm text-white">
                                  {caseBlock.description}
                                </p>
                              </div>

                              {/* PC端：鼠标悬停时显示的模态框 */}
                              <div className="hidden lg:block absolute bottom-0 left-0 right-0 bg-black/80 text-white px-6 pt-5 pb-7 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300 ease-in-out">
                                <h3 className="text-[20px] mb-4 font-bold">
                                  {caseBlock.title}
                                </h3>
                                <p className="text-sm text-white">
                                  {caseBlock.description}
                                </p>
                              </div>
                            </>
                          ) : (
                            // 如果没有图片，显示占位符
                            <div className="w-full h-auto lg:w-[340px] lg:h-[420px] bg-gray-200 flex items-center justify-center">
                              <div className="text-center p-6">
                                <h3 className="mb-2 text-xl font-semibold text-gray-800">
                                  {caseBlock.title}
                                </h3>
                                <p className="text-sm text-gray-600">{caseBlock.description}</p>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 按钮区域 */}
                {buttons && buttons.length > 0 && (
                  <div className="text-center">
                    <div className="flex justify-center gap-4">
                      {buttons.map((button: any, idx: number) => (
                        <button
                          key={idx}
                          className="bg-black px-11 lg:px-12 py-1.5 lg:py-3 text-sm lg:text-base text-white transition-colors hover:bg-gray-800"
                        >
                          {button.label}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </section>
        )
      }

      // 处理医生信息
      if (title === 'Authorised doctors' || title === '授权医生') {
        return (
          <section className="py-10 lg:py-20">
            <div className="container mx-auto px-4 lg:px-0">
              <div className="flex h-full flex-col justify-between">
                <div>
                  <h2 className="mb-10 lg:mb-15 text-center text-[20px] lg:text-4xl font-[30px]">{title}</h2>

                  {/* 医生网格 */}
                  <div className="mb-10 grid grid-cols-2 gap-4 md:gap-8 lg:grid-cols-4">
                    {block.blocks?.map((doctorBlock: any, idx: number) => (
                      <div key={idx} className="">
                        <div className="relative mb-4">
                          {doctorBlock.media?.url ? (
                            <Image
                              src={doctorBlock.media.url}
                              alt={doctorBlock.title || `Doctor ${idx + 1}`}
                              width={200}
                              height={250}
                              className="h-64 w-full object-cover"
                            />
                          ) : (
                            // 如果没有图片，显示占位符
                            <div className="flex h-64 w-full items-center justify-center bg-gray-200">
                              <div className="text-center">
                                <div className="mx-auto mb-2 h-16 w-16 rounded-full bg-gray-400"></div>
                                <p className="text-sm text-gray-500">医生照片</p>
                              </div>
                            </div>
                          )}
                        </div>

                        {/* 医生信息 */}
                        <h3 className="mb-1 text-sm lg:text-lg font-semibold">
                          {doctorBlock.title || `Dr. ${idx + 1}`}
                        </h3>
                        <p className="text-xs lg:text-sm text-gray-600">
                          { doctorBlock.subtitle || doctorBlock.description }
                        </p>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 按钮区域 */}
                {buttons && buttons.length > 0 && (
                  <div className="text-center">
                    <div className="flex justify-center gap-4">
                      {buttons.map((button: any, idx: number) => (
                        <button
                          key={idx}
                          className="bg-black px-11 lg:px-12 py-1.5 lg:py-3 text-sm lg:text-base text-white transition-colors hover:bg-gray-800"
                        >
                          {button.label}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </section>
        )
      }

      return null

    default:
      return null
  }
}

export default async function Home({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params
  const name = locale === 'en-US' ? 'home' : '首页'
  const pageData = await getPage(locale, name)
  console.log(pageData?.blocks)
  return (
    <div className="pt-16 lg:pt-0">
      {/* Hero Banner */}
      <div className="relative">
        {pageData?.heroes.map((item: any) => (
          <video
            key={item.id}
            src={item.media.url}
            className="w-full h-auto lg:h-screen lg:object-cover object-contain"
            autoPlay
            muted
            loop
          />
        ))}
      </div>

      {/* Blocks Content */}
      <div className="bg-[#FAF6F2]">
        <div className="md:w-[76%] md:max-w-none md:mx-auto">
          {pageData?.blocks.map((block: any, index: number) => (
            <div key={block.id || index}>
              <BlockRenderer block={block} />
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}